# AI Provider API Keys
# Configure at least one of these for AI functionality to work
# The system will try providers in order: Gemini -> OpenRouter -> Groq

# Google Gemini API Key
# Get your key from: https://aistudio.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# OpenRouter API Key (for DeepSeek and other models)
# Get your key from: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Groq API Key
# Get your key from: https://console.groq.com/keys
GROQ_API_KEY=your_groq_api_key_here

# Legacy support (same as GEMINI_API_KEY)
API_KEY=your_gemini_api_key_here
