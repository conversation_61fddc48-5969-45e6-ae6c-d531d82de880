
import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import { TaskData, FollicleScanData, Patient, PatientHistoryData, SpermAnalysisData, HcgData } from "../types";

let ai: GoogleGenAI | null = null;

function getAiClient(): GoogleGenAI | null {
  if (ai) return ai;
  const apiKey = process.env.API_KEY;
  if (apiKey) {
    ai = new GoogleGenAI({ apiKey });
    return ai;
  }
  console.warn("API_KEY for Gemini is not set. AI features will be disabled.");
  return null;
}

const cleanJsonString = (jsonStr: string): string => {
  let cleaned = jsonStr.trim();
  const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
  const match = cleaned.match(fenceRegex);
  if (match && match[2]) {
    cleaned = match[2].trim();
  }
  return cleaned;
};

const getPromptForTask = (taskTitle: string, context: { taskData: TaskData; patient?: Patient }): string => {
  const { taskData, patient } = context;
  const dataString = JSON.stringify(taskData, null, 2);
  let prompt = `You are an expert AI assistant for an IVF clinic. Analyze the provided data for the task "${taskTitle}". 
    Provide concise, actionable suggestions for specific fields in a JSON object format.
    The keys of the JSON object must correspond to the fields in the input data.
    Only provide suggestions for fields where you have a high-confidence recommendation. If no suggestion, omit the key.`;
  
  if (taskTitle.includes('Follicle Scan')) {
    prompt += `\n\nFor this Follicle Scan, analyze the measurements and any provided image.
    Input Data:
    ${dataString}

    Provide suggestions for 'endometriumThickness' (as a number), 'endometriumPattern' (as 'Trilaminar', 'Homogenous', or 'Other'), and 'notes' (a brief summary).
    Example JSON output:
    { "endometriumThickness": 9.5, "endometriumPattern": "Trilaminar", "notes": "Endometrial lining appears receptive. Follicular growth is homogenous." }`
  } else if (taskTitle.includes('Psychological Assessment')) {
      prompt = `You are a clinical psychologist specializing in fertility counseling. Based on the provided clinical, emotional, and financial needs, please perform two tasks:
      1. Generate a concise "Persona": A 2-3 sentence summary that captures the patient's or couple's psychological archetype (e.g., "The Anxious Planners," "The Resilient Optimists," "The Guarded Hopefuls").
      2. Create an "Intervention Plan": A bulleted list of specific, actionable recommendations for the counselor to support the patient(s). Use '\\n-' to separate bullet points.
      
      Input Data:
      ${dataString}

      Provide your response in a valid JSON object format with exactly two keys: "persona" and "interventionPlan".
      Example JSON output:
      {
        "persona": "This couple presents as 'The Guarded Researchers.' They are highly informed and analytical, using data to manage their anxiety, but this intellectual approach may mask deeper fears of disappointment.",
        "interventionPlan": "- Schedule a joint session to practice communication techniques.\\n- Provide resources for financial planning for fertility treatments.\\n- Introduce mindfulness and relaxation exercises to manage procedure-related anxiety."
      }`
  } else if (taskTitle === 'Sperm Analysis') {
    const { count, motility, morphology } = taskData as SpermAnalysisData;
    prompt = `You are a senior clinical embryologist. Based on WHO 2021 criteria (Count > 16M/mL, Motility > 42% progressive, Morphology > 4% normal forms), analyze the following sperm parameters:
    - Count: ${count} million/mL
    - Motility: ${motility}%
    - Morphology: ${morphology}%

    Your Task:
    1. Provide a concise 'assessment' term for the sample (e.g., Normozoospermia, Teratozoospermia, Oligoasthenoteratozoospermia (OAT)).
    2. Provide a 'recommendation' for the fertilization method (Standard IVF or ICSI) based on the parameters. Severe defects in any parameter should warrant an ICSI recommendation.

    Provide your response in a valid JSON object format with the keys "assessment" and "recommendation".
    Example JSON output:
    {
        "assessment": "Oligoasthenoteratozoospermia (OAT)",
        "recommendation": "ICSI is strongly recommended due to low count, motility, and morphology."
    }`;
  } else if (taskTitle === 'Day 5 Check & Grading') {
      prompt = `You are an expert embryologist. Analyze the provided embryo image.
      
      Your Task:
      1. Suggest a blastocyst 'grade' using the Gardner grading system (e.g., '4AA', '3BC', '5AB').
      2. Provide brief 'notes' explaining your reasoning based on expansion, inner cell mass (ICM), and trophectoderm (TE) quality visible in the image.

      Provide your response in a valid JSON object with the keys "grade" and "notes".
      Example JSON output:
      {
          "grade": "4AA",
          "notes": "Fully expanded blastocyst with a tightly packed, prominent ICM and a cohesive trophectoderm with many cells."
      }`;
  } else if (taskTitle === 'hCG Blood Test') {
    const { hcgValue } = taskData as HcgData;
    const daysPostTransfer = 14; // Assuming a standard Day 14 test
    prompt = `You are a clinical nurse specialist. A patient's hCG blood test was taken ${daysPostTransfer} days after a Day 5 embryo transfer.
    
    The patient's result is: ${hcgValue} mIU/mL.

    Your Task:
    Provide a clinical 'interpretation' for this result. Use one of the following interpretations:
    - 'Positive, consistent with a viable pregnancy.' (for values > 50)
    - 'Low positive. Repeat test in 48-72 hours to confirm viability.' (for values between 5 and 50)
    - 'Negative.' (for values < 5)

    Provide your response in a valid JSON object with a single key: "interpretation".
    Example JSON output:
    { "interpretation": "Positive, consistent with a viable pregnancy." }`;
  } else if (taskTitle === 'Prescribe Medication' && patient) {
    const historyTask = patient.pathway.find(p => p.stepName === 'Initial Consultation')?.tasks.find(t => t.title === 'Review Patient History');
    const historyData = historyTask?.data as PatientHistoryData | undefined;

    prompt = `You are an expert reproductive endocrinologist providing clinical decision support. Based on the following patient data, suggest a medication protocol for ovarian stimulation.

    Patient Data:
    - Age: ${patient.age}
    - Protocol: ${patient.protocol}
    - Diagnoses: ${JSON.stringify(historyData?.diagnoses || 'N/A')}
    - Previous IVF Cycles: ${historyData?.previousIVFCycles || 0}
    - Notes: ${historyData?.notes || 'N/A'}

    Your Task:
    1. Recommend a primary gonadotropin (e.g., Gonal-F, Menopur, Follistim).
    2. Suggest a starting dosage in IU, frequency (e.g., 'Daily'), and an initial duration in days.
    3. Provide a concise clinical 'explanation' for your choices, citing the patient's specific data points. For example: "Given the patient's age and PCOS diagnosis, a conservative starting dose of Gonal-F is recommended to mitigate OHSS risk."

    Provide your response in a valid JSON object format with the following keys: "medication", "dosage", "frequency", "duration", "explanation".
    Example JSON output:
    {
      "medication": "Gonal-F",
      "dosage": "150 IU",
      "frequency": "Daily",
      "duration": "10 days",
      "explanation": "Given the patient's age of ${patient.age} and diagnosis of PCOS, a standard dose of Gonal-F is recommended to ensure a good follicular response while monitoring closely for signs of OHSS."
    }`;
  } else {
    prompt += `
    Input Data:
    ${dataString}

    Provide your suggestions in the following JSON format:
    { "fieldName": "Your suggestion here..." }`;
  }

  return prompt;
};

export const getAIAnalysis = async (
  taskTitle: string,
  context: { taskData: TaskData; patient?: Patient },
  imageBase64?: string
): Promise<Record<string, any>> => {
  const aiClient = getAiClient();
  if (!aiClient) {
    return Promise.resolve({
      error: "AI analysis is unavailable. API key not configured.",
    });
  }

  const prompt = getPromptForTask(taskTitle, context);
  const contents = [];

  const textPart = { text: prompt };
  contents.push(textPart);

  if (imageBase64) {
    // Ensure base64 string doesn't have the data URL prefix
    const pureBase64 = imageBase64.split(',').pop() || '';
    const imagePart = {
      inlineData: {
        mimeType: 'image/jpeg', // Assuming jpeg, could be dynamic
        data: pureBase64,
      },
    };
    contents.push(imagePart);
  }

  try {
    const response: GenerateContentResponse = await aiClient.models.generateContent({
        model: "gemini-2.5-flash-preview-04-17",
        contents: { parts: contents },
        config: {
          responseMimeType: "application/json",
        },
    });

    const jsonStr = cleanJsonString(response.text);
    return JSON.parse(jsonStr);

  } catch (error) {
    console.error("Error fetching AI analysis:", error);
    throw new Error("Failed to get analysis from Gemini API.");
  }
};
