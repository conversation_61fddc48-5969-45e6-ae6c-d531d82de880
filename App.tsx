
import React, { useState, useEffect, useMemo } from 'react';
import { Patient, PatientPathwayStep, UserRole, Task, NewPatientOnboardingInfo } from './types';
import { getAllPatients, createNewPatient } from './services/ivfDataService';
import Header from './components/Header';
import PatientPathway from './components/PatientPathway';
import ClinicDashboard from './components/ClinicDashboard';
import QualityDashboard from './components/QualityDashboard';
import ExecutiveDashboard from './components/ExecutiveDashboard';
import { LoadingIcon } from './components/icons';

type AppView = 'clinic' | 'quality' | 'patient' | 'executive';

const App: React.FC = () => {
  const [allPatients, setAllPatients] = useState<Patient[]>([]);
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentUserRole, setCurrentUserRole] = useState<UserRole>(UserRole.Doctor);
  const [activeView, setActiveView] = useState<AppView>('executive');

  useEffect(() => {
    const fetchInitialData = async () => {
      setIsLoading(true);
      try {
        const data = await getAllPatients();
        setAllPatients(data);
      } catch (err) {
        setError('Failed to fetch patient data.');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };
    fetchInitialData();
  }, []);

  const handleUpdatePatientData = (updatedTask: Task, stepId: string, patientId: string) => {
      setAllPatients(prevPatients => 
          prevPatients.map(p => {
              if (p.id !== patientId) return p;
              
              const updatedPathway = p.pathway.map(step => {
                  if (step.stepId !== stepId) return step;
                  return {
                      ...step,
                      tasks: step.tasks.map(task => 
                          task.id === updatedTask.id ? updatedTask : task
                      )
                  };
              });

              return { ...p, pathway: updatedPathway };
          })
      );
  };
  
  const handleAddNewPatient = (patientInfo: NewPatientOnboardingInfo) => {
    const newPatient = createNewPatient(patientInfo);
    setAllPatients(prevPatients => [...prevPatients, newPatient]);
  };

  const handleSelectPatient = (patientId: string) => {
    setSelectedPatientId(patientId);
    setActiveView('patient');
  };

  const handleNavigate = (view: AppView) => {
    if (view !== 'patient') {
      setSelectedPatientId(null);
    }
    setActiveView(view);
  };
  
  const selectedPatient = useMemo(() => {
    if (!selectedPatientId) return null;
    return allPatients.find(p => p.id === selectedPatientId) || null;
  }, [selectedPatientId, allPatients]);

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center h-96">
          <LoadingIcon className="h-12 w-12 animate-spin" />
          <p className="ml-4 text-lg">Loading Clinic Data...</p>
        </div>
      );
    }

    if (error) {
      return <div className="text-center text-red-500">{error}</div>;
    }

    switch (activeView) {
      case 'executive':
        return <ExecutiveDashboard patients={allPatients} />;
      case 'clinic':
        return (
          <ClinicDashboard
            patients={allPatients}
            onSelectPatient={handleSelectPatient}
            currentUserRole={currentUserRole}
            onAddNewPatient={handleAddNewPatient}
          />
        );
      case 'quality':
        return <QualityDashboard />;
      case 'patient':
        return selectedPatient ? (
           <PatientPathway
              patient={selectedPatient}
              onUpdate={handleUpdatePatientData}
              currentUserRole={currentUserRole}
            />
        ) : (
          <div className="text-center text-slate-500">No patient selected. Please return to the dashboard.</div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-slate-100 text-slate-800 dark:bg-slate-900 dark:text-slate-200 font-sans">
      <Header 
        activeView={activeView}
        onNavigate={handleNavigate}
        currentUserRole={currentUserRole}
        onRoleChange={setCurrentUserRole}
        selectedPatientName={selectedPatient?.name}
      />
      <main className="p-4 sm:p-6 lg:p-8">
        <div className="max-w-screen-2xl mx-auto">
          {renderContent()}
        </div>
      </main>
    </div>
  );
};

export default App;
