
# AI-Assisted IVF EMR

## Overview

This project is a high-fidelity, interactive prototype of an AI-assisted Electronic Medical Record (EMR) system specifically designed for In Vitro Fertilization (IVF) clinics. It moves beyond traditional EMRs by integrating intelligent features directly into the clinical workflow, focusing on improving data quality, providing decision support, and optimizing clinic resource management.

The application is built around the patient's treatment pathway, providing a clear, role-based, and time-aware view of all clinical tasks.

## Key Features

### 1. The "Mission Control" Clinic Dashboard
The dashboard provides a high-level, operational view of the entire clinic.
-   **Resource-Aware Timeline:** A visual Gantt chart-style timeline displays all appointments, organized into "swimlanes" for each critical resource (Doctor, Nurse, Embryologist) and facility (Operating Theater, Lab).
-   **Automatic Conflict Detection:** The system automatically identifies and visually flags scheduling conflicts, such as a doctor being double-booked or a facility being used for two procedures simultaneously.
-   **Interactive Agenda & Day Navigation:** A "natural" agenda view lists all of the day's appointments chronologically, and a date picker allows for easy navigation to plan future schedules.
-   **Patient-Centric Color Coding:** Events on the timeline are colored by patient, making it easy to track a patient's journey throughout the day.

### 2. The AI-Powered & "Fool-Proof" Patient Pathway
The core of the application is the detailed patient view, designed to guide clinicians and ensure data integrity.
-   **"Prep -> Action -> Post" Workflow:** Major clinical events (e.g., Egg Retrieval) are broken down into a three-step process, ensuring safety checklists are completed *before* a procedure begins.
-   **Connected Data Flow:** The application features a stateful, interconnected workflow. Data from one task (e.g., number of oocytes identified) automatically informs the subsequent, dependent task (e.g., fertilization), reducing errors and redundant data entry.
-   **AI-Assisted Decision Support:** Google Gemini is deeply integrated to provide genuine clinical value:
    -   **Medication Suggestions:** Recommends medication protocols based on patient history and diagnosis.
    -   **Sperm/Embryo Analysis:** Provides clinical assessments for sperm analysis and suggests grades for embryo images.
    -   **Psychological Personas:** Generates patient personas and intervention plans based on counseling notes.
-   **Guided Data Entry ("Meta Prompts"):** All forms include detailed placeholder text and templates, guiding users to enter complete, structured, and high-quality data with minimal typing.

### 3. Comprehensive Patient Onboarding
-   A single, comprehensive modal form allows for creating a new patient, capturing their full initial history, and dynamically generating their entire scheduled treatment pathway in one step.

### 4. Foundational Quality Dashboard
-   A dedicated hub for clinic analytics, featuring a detailed breakdown of key performance indicators (KPIs) with progress bars against set targets. The tabbed interface is designed to be easily expandable with new reports.

## Technology Stack

-   **Frontend:** React, TypeScript, Tailwind CSS
-   **AI Integration:** Multi-provider support with automatic fallback
    -   Google Gemini API (`@google/genai`)
    -   OpenRouter API (DeepSeek and other models)
    -   Groq API (`groq-sdk`)

## Architecture: "Smart Frontend, Thin Backend"

This application is architected with a "Smart Frontend, Thin Backend" philosophy. The majority of the UI logic, state management, and client-side calculations (like conflict detection) reside in the frontend.

This approach allows for a highly responsive user experience and an agile development process. The backend (once implemented) can be kept "lite," with its primary responsibilities being:
1.  Data Persistence (CRUD API).
2.  User Authentication & Authorization.
3.  Securely proxying requests to third-party services like the Google Gemini API.
4.  Handling atomic business logic (like new patient creation).

## File Structure Overview

-   `index.html` & `index.tsx`: The main entry points for the application.
-   `App.tsx`: The root React component, managing global state and routing between the main views.
-   `/services`: Contains data-handling logic.
    -   `ivfDataService.ts`: A mock data service that simulates a backend API, generating patient data and pathways. **This is the file to replace with real API calls.**
    -   `geminiService.ts`: Handles communication with the Google Gemini API. **In a production app, this logic would move to the backend.**
    -   `qualityDataService.ts`: Provides static data for the quality dashboard.
-   `/components`: Contains all React components.
    -   `/forms`: Contains all the detailed, interactive forms for each clinical task.
    -   `/ui`: Contains generic, reusable UI elements like `ProgressBar`.
    -   `ClinicDashboard.tsx`: The main "mission control" view.
    -   `PatientPathway.tsx`: The detailed single-patient view.
    -   `QualityDashboard.tsx`: The container for quality reports.
-   `types.ts`: Defines all TypeScript interfaces and enums for the application's data structures.
-   `constants.ts`: Contains application-wide constants like UI colors, role permissions, and static lists.

## Next Steps: The Path to a Production System

To evolve this prototype into a fully functional, deployable EMR, the following steps are required:

1.  **Backend & Database Implementation:**
    -   **Action:** Replace `ivfDataService.ts` with real API calls to a backend server.
    -   **Details:** Design and build a database schema based on `types.ts`. Create a "lite" backend API (e.g., using Node.js/Express) that exposes endpoints for `GET`, `POST`, and `PUT` operations on patients and tasks.

2.  **User Authentication & Authorization:**
    -   **Action:** Implement a secure login system.
    -   **Details:** The backend will manage user accounts and generate secure tokens (e.g., JWT). The frontend will store this token and send it with every API request. The backend will validate the token and the user's role before allowing any action.

3.  **Secure AI API Proxy:**
    -   **Action:** Move the Gemini API key and all calls from `geminiService.ts` to the backend.
    -   **Details:** Create a new endpoint on our own backend (e.g., `/api/ai/analyze`). The frontend will call this endpoint, and the server will securely add the API key and forward the request to Google.

4.  **Real-Time Collaboration:**
    -   **Action:** Implement a real-time update mechanism using WebSockets.
    -   **Details:** When one user completes a task, the server will push updates to all other relevant, connected clients, ensuring the dashboard and patient views are always in sync without needing a manual refresh.

5.  **Dynamic Quality Dashboards:**
    -   **Action:** Connect the quality dashboard to the live database.
    -   **Details:** The metrics will be calculated via dynamic database queries (e.g., `SELECT COUNT(*) FROM...`) instead of being hard-coded. This will provide real, up-to-date insights into clinic performance.

6.  **Admin Configuration Panel:**
    -   **Action:** Build a settings area for clinic administrators.
    -   **Details:** This would allow an admin to manage user accounts, define clinic protocols, and customize checklist items without requiring code changes.

## AI Provider Configuration

This application now supports multiple AI providers with automatic fallback functionality:

### Supported Providers (in order of preference):
1. **Google Gemini** - Primary provider with vision capabilities
2. **OpenRouter (DeepSeek)** - Fallback provider with competitive performance
3. **Groq** - Fast inference fallback (text-only)

### Setup Instructions:

1. **Copy the environment template:**
   ```bash
   cp .env.example .env
   ```

2. **Configure API keys** (at least one required):
   ```bash
   # Google Gemini (recommended for vision tasks)
   GEMINI_API_KEY=your_gemini_api_key_here

   # OpenRouter (for DeepSeek and other models)
   OPENROUTER_API_KEY=your_openrouter_api_key_here

   # Groq (for fast text inference)
   GROQ_API_KEY=your_groq_api_key_here
   ```

3. **Get API Keys:**
   - **Gemini:** Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
   - **OpenRouter:** Visit [OpenRouter Keys](https://openrouter.ai/keys)
   - **Groq:** Visit [Groq Console](https://console.groq.com/keys)

### How Fallback Works:
- The system tries providers in order: Gemini → OpenRouter → Groq
- If one provider fails, it automatically tries the next available provider
- Only configured providers (with valid API keys) are attempted
- Error messages indicate which providers were tried and why they failed

### Provider Capabilities:
- **Gemini:** Full support including image analysis for embryo grading and follicle scans
- **OpenRouter:** Full support including image analysis via DeepSeek model
- **Groq:** Text-only analysis (images are described in text for processing)

### Development:
- Use the `AIProviderStatus` component to debug provider availability
- Check browser console for detailed fallback logs
- Provider status is displayed in development mode
